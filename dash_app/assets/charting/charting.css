/* Chart-specific CSS variables and layout */
.chart-container { --bg:#0e1116; --panel:#161b22; --text:#c9d1d9; --muted:#8b949e; --accent:#58a6ff; }
.chart-layout { height:100%; }

/* Grid Layout: Sidebar + Symbol Bar + Chart + Trade Details */
.chart-body {
  margin: 0;
  display: grid;
  grid-template-areas:
    "sidebar symbol-bar"
    "sidebar chart"
    "trade-details trade-details";
  grid-template-columns: auto 1fr;
  grid-template-rows: auto 1fr auto;
  background: var(--bg);
  color: var(--text);
  font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial;
}

/* Symbol/Timeframe Bar */
.symbol-bar {
  grid-area: symbol-bar;
  background: var(--panel);
  border-bottom: 1px solid #222;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 50px;
  padding-bottom: 50px;
}

.symbol-search {
  display: flex;
  align-items: center;
  gap: 8px;
}

.symbol-input {
  background: #1a1f2e;
  border: 1px solid #2b3340;
  border-radius: 6px;
  padding: 6px 12px;
  color: var(--text);
  font-size: 14px;
  font-weight: 600;
  min-width: 120px;
  outline: none;
}

.symbol-input:focus {
  border-color: #58a6ff;
  box-shadow: 0 0 0 2px #58a6ff20;
}

.symbol-input::placeholder {
  color: var(--muted);
  font-weight: normal;
}

.timeframe-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.timeframe-btn {
  background: transparent;
  border: 1px solid #2b3340;
  color: var(--muted);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 28px;
  text-align: center;
}

.timeframe-btn:hover {
  border-color: #3a4556;
  color: var(--text);
}

.timeframe-btn.active {
  background: #58a6ff;
  border-color: #58a6ff;
  color: #ffffff;
}

.chart-type-buttons {
  display: flex;
  gap: 4px;
  margin-left: auto;
}

.chart-type-btn {
  background: transparent;
  border: 1px solid #2b3340;
  color: var(--muted);
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-type-btn:hover {
  border-color: #3a4556;
  color: var(--text);
}

.chart-type-btn.active {
  background: #58a6ff20;
  border-color: #58a6ff;
  color: #58a6ff;
}

/* Chart Area */
.chart-area {
  grid-area: chart;
  position: relative;
}

#chart {
  width: 100%;
  height: 100%;
}

/* Vertical Button Sidebar - Centered */
.button-sidebar {
  grid-area: sidebar;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px 10px;
  background: var(--panel);
  border-right: 1px solid #222;
  min-width: 80px;
  max-width: 80px;
  align-items: center;
  justify-content: space-evenly;
  height: 100%
}

/* Button Groups with Visual Separation */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  padding: 4px 0;
}

.button-group:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 1px;
  background: #2b3340;
}

/* Vertical Chart Buttons */
.chart-btn-vertical {
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-size: 18px;
  background: #222833;
  color: var(--text);
  border: 1px solid #2b3340;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px 4px;
  box-sizing: border-box;
  position: relative;
}

.chart-btn-vertical:hover {
  border-color: #3a4556;
  background: #2a3441;
}

.chart-btn-vertical.active {
  border: 2px solid #58a6ff;
  background: #1a2332;
  box-shadow: 0 0 0 1px #58a6ff40;
}

/* Button text labels */
.chart-btn-vertical::after {
  content: attr(data-label);
  font-size: 9px;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  margin-top: 2px;
  opacity: 0.9;
}

/* Bottom Trade Details Panel */
.trade-details-bottom {
  grid-area: trade-details;
  background: var(--panel);
  border-top: 1px solid #222;
  padding: 12px 16px;
  max-height: 200px;
  overflow-y: auto;
  display: none; /* Hidden by default */
}

.trade-details-bottom.show {
  display: block;
}

.trade-details-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Trade Details Horizontal Layout */
.trade-plan-header {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #2b3340;
}

.trade-plan-title {
  font-weight: bold;
  font-size: 14px;
}

.trade-plan-summary {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.trade-levels {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.trade-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.trade-section-title {
  font-weight: bold;
  font-size: 11px;
  margin-bottom: 4px;
  opacity: 0.8;
}

/* Updated Trade Entry Styling for Bottom Panel */
.trade-entry {
  background: #0b1d33;
  border: 1px solid #14304f;
  border-radius: 4px;
  padding: 6px 8px;
  margin: 2px 0;
  font-size: 11px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trade-entry.stop-loss {
  border-color: #dc3545;
  background: #2d0a0f;
}

.trade-entry.entry {
  border-color: #28a745;
  background: #0a2d0f;
}

.trade-entry.take-profit {
  border-color: #ffc107;
  background: #2d2a0a;
}

/* Drawings Panel (keep for drawings list) */
.drawings-panel {
  position: absolute;
  top: 60px;
  right: 10px;
  background: var(--panel);
  border: 1px solid #222;
  border-radius: 8px;
  padding: 10px;
  min-width: 280px;
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
}

.drawings-panel.show {
  display: block;
}

/* Utility Classes */
.muted {
  color: var(--muted);
  font-size: 12px;
}

.chart-list {
  list-style: none;
  padding: 0;
  margin: 8px 12px 12px;
}

.chart-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  border: 1px solid #2b3340;
  border-radius: 8px;
  margin-top: 8px;
}

.pill {
  background: #0b1d33;
  border: 1px solid #14304f;
  border-radius: 999px;
  padding: 2px 8px;
  font-variant-numeric: tabular-nums;
}

.groupTag {
  font-size: 11px;
  opacity: 0.7;
  border: 1px solid #2b3340;
  border-radius: 6px;
  padding: 0 6px;
  margin-left: 6px;
}

.stack {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-body {
    grid-template-areas:
      "chart"
      "sidebar"
      "trade-details";
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto auto;
  }

  .button-sidebar {
    flex-direction: row;
    overflow-x: auto;
    min-width: unset;
    max-width: unset;
    border-right: none;
    border-top: 1px solid #222;
    padding: 8px 12px;
    justify-content: flex-start;
  }

  .button-group {
    flex-direction: row;
    gap: 6px;
  }

  .button-group:not(:last-child)::after {
    display: none;
  }

  .chart-btn-vertical {
    width: 50px;
    height: 50px;
    font-size: 14px;
  }

  .chart-btn-vertical::after {
    font-size: 8px;
  }

  .mode-hint {
    display: none;
  }
}


/* Loading overlay */
.loading-overlay{position:absolute;inset:0;display:none;align-items:center;justify-content:center;background:#0e1116cc;color:#c9d1d9;z-index:500;font-weight:600}
.loading-overlay.show{display:flex}

/* Trade panels container */
.trade-panels-container{display:flex;width:100%;grid-area:trade-details;grid-column:1/-1;gap:12px;padding:12px;background:#0e1116;border-top:1px solid #2b3340;align-items:top;justify-content:center;flex-wrap:wrap;box-sizing:border-box;overflow-x:hidden}
.trade-setups-panel,.trade-details-panel{flex:1 1 calc(50% - 6px);max-width:calc(50% - 6px);background:#1a1f2e;border:1px solid #2b3340;border-radius:6px;overflow:hidden;height:fit-content;box-sizing:border-box}
.panel-header{padding:8px 12px;background:#222833;border-bottom:1px solid #2b3340;font-size:12px;font-weight:600;color:#c9d1d9}
.trade-details-content{padding:12px;min-height:120px}
.trade-setups-list{max-height:200px;overflow-y:auto;min-height:120px}
.empty-state{padding:20px;text-align:center}
.empty-state .muted{font-size:12px;color:#7d8590;margin-bottom:4px}
.empty-state .instruction{font-size:11px;color:#656d76}
.play-trade-container{margin-top:12px;text-align:center}
.play-trade-btn{background:#238636;color:white;border:none;padding:6px 16px;border-radius:4px;font-size:12px;cursor:pointer;font-weight:500}
.play-trade-btn:hover{background:#2ea043}
.trade-setup-item{padding:8px 12px;border-bottom:1px solid #2b3340;font-size:12px}
.trade-setup-item:last-child{border-bottom:none}
.trade-setup-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:4px}
.trade-setup-symbol{font-weight:600;color:#c9d1d9}
.trade-setup-type{color:#7d8590;font-size:11px}
.trade-setup-details{color:#7d8590;font-size:11px}
.trade-setup-remove{background:none;border:none;color:#f85149;cursor:pointer;font-size:11px;padding:2px 4px}
.trade-setup-remove:hover{color:#ff7b72}