// Trade Setups Management
// Handles adding, displaying, and managing trade setups created from chart drawings

let tradeSetups = [];

// Initialize trade setups functionality
function initTradeSetups() {
  const playTradeBtn = document.getElementById('play-trade-btn');
  if (playTradeBtn) {
    playTradeBtn.addEventListener('click', handlePlayTrade);
  }
}

// Show/hide the Play Trade button based on whether trade details are available
function togglePlayTradeButton(show) {
  const container = document.getElementById('play-trade-container');
  if (container) {
    container.style.display = show ? 'block' : 'none';
  }
}

// Handle Play Trade button click
function handlePlayTrade() {
  const tradeInfo = document.getElementById('trade-info');
  if (!tradeInfo || !window.currentFibDetails) {
    console.warn('No trade details available to play');
    return;
  }

  // Create trade setup from current fib details
  const setup = {
    id: crypto?.randomUUID?.() || Math.random().toString(36).slice(2),
    symbol: window.currentSymbol || 'UNKNOWN',
    exchange: window.currentExchange || 'binance',
    type: 'Fibonacci',
    timestamp: Date.now(),
    details: { ...window.currentFibDetails }
  };

  // Add to setups list
  tradeSetups.push(setup);
  
  // Update UI
  renderTradeSetups();
  
  // Hide play button after adding
  togglePlayTradeButton(false);
  
  console.log('Trade setup added:', setup);
}

// Render all trade setups in the list
function renderTradeSetups() {
  const listEl = document.getElementById('trade-setups-list');
  if (!listEl) return;

  if (tradeSetups.length === 0) {
    listEl.innerHTML = `
      <div class="empty-state">
        <div class="muted">No trade setups yet</div>
        <div class="instruction">Draw a Fibonacci retracement, then click "Play Trade" to add it here</div>
      </div>
    `;
    return;
  }

  listEl.innerHTML = tradeSetups.map(setup => `
    <div class="trade-setup-item" data-setup-id="${setup.id}">
      <div class="trade-setup-header">
        <div>
          <span class="trade-setup-symbol">${setup.symbol}</span>
          <span class="trade-setup-type">${setup.type}</span>
        </div>
        <button class="trade-setup-remove" onclick="removeTradeSetup('${setup.id}')">×</button>
      </div>
      <div class="trade-setup-details">
        ${formatTradeSetupDetails(setup)}
      </div>
    </div>
  `).join('');
}

// Format trade setup details for display
function formatTradeSetupDetails(setup) {
  if (setup.type === 'Fibonacci' && setup.details) {
    const d = setup.details;
    return `
      P1: ${d.p1?.toFixed(2) || 'N/A'} → P2: ${d.p2?.toFixed(2) || 'N/A'}<br>
      Entries: ${d.fib50?.toFixed(2) || 'N/A'}, ${d.fib618?.toFixed(2) || 'N/A'}, ${d.fib786?.toFixed(2) || 'N/A'}<br>
      TPs: ${d.fibtp236?.toFixed(2) || 'N/A'}, ${d.fibtp382?.toFixed(2) || 'N/A'}
    `;
  }
  return 'Details not available';
}

// Remove a trade setup
function removeTradeSetup(setupId) {
  tradeSetups = tradeSetups.filter(setup => setup.id !== setupId);
  renderTradeSetups();
  console.log('Trade setup removed:', setupId);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initTradeSetups);

// Export functions for use by other modules
window.togglePlayTradeButton = togglePlayTradeButton;
window.removeTradeSetup = removeTradeSetup;
