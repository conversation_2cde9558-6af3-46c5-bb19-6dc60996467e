import os
import re
import dash
from dash import callback_context, State
from dash import Dash, html, dcc, Input, Output, callback_context
# from dash_extensions.enrich import DashProxy, Output, Input, State, MATCH, ALL, set_props, Trigger, callback_context, html, dcc

import dash_bootstrap_components as dbc
from dash_bootstrap_templates import load_figure_template
from flask import session, redirect

from dash_app.widgets.dashboard_widgets import get_callbacks
from dash_app.widgets.search_bar import get_search_bar_callbacks

# Handle Dash Logging (reducing for debugging)
import logging
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)  # or logging.CRITICAL

# Available themes
THEMES = {name: theme for name, theme in vars(dbc.themes).items() if name.isupper()}

# Default theme
DEFAULT_THEME = dbc.themes.COSMO

external_stylesheets = [DEFAULT_THEME, "https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css"]
app = Dash(__name__, external_stylesheets=external_stylesheets,
           use_pages=True,
           assets_ignore=r".*\.js$",  # ignore all JS in assets/charting
           suppress_callback_exceptions=True)

# Set Flask secret key for session management
app.server.secret_key = os.environ.get("SECRET_KEY", "supersecretkey123")

# Extract and apply the figure template based on the initial theme
pattern = r'/dist/([^/]+)/bootstrap.min.css'
match = re.search(pattern, DEFAULT_THEME)
if match:
    load_figure_template(match.group(1))

# Navbar with Theme Switcher
navbar = dbc.NavbarSimple(
    children=[
        dbc.NavItem(dbc.NavLink("Chart", href="chart", id="nav-chart", className="fs-7")),
        dbc.NavItem(dbc.NavLink("Dashboard", href="dashboard", id="nav-dashboard", className="fs-7")),
        dbc.NavItem(dbc.NavLink("Analytics", href="analytics", id="nav-analytics", className="fs-7")),
        dbc.NavItem(dbc.NavLink("Strategies", href="strategies", id="nav-strategies", className="fs-7")),
        dbc.NavItem(dbc.NavLink("Controls", href="controls", id="nav-controls", className="fs-7")),
        dbc.NavItem(dbc.Button(id="login-logout-button")),

        # Theme Selector Dropdown
        dbc.DropdownMenu(
            label="Theme",
            children=[
                dbc.DropdownMenuItem(theme_name, id=f"theme-{theme_name}", n_clicks=0)
                for theme_name in THEMES
            ],
            nav=True,
            in_navbar=True,
            id="theme-dropdown",
        ),
    ],
    brand=html.Img(src="/assets/logo3.png", height="30px", alt="TradeCraft Logo"),
    brand_href="",
    color="primary",
    dark=True,
)

app.layout = html.Div([
    dcc.Store(id="theme-store", data={"theme": DEFAULT_THEME}),
    html.Div(id="nav-bar", children=[navbar]),
    dash.page_container,
    dcc.Location(id="url", refresh=True),
    dcc.Interval(id="startup-check", interval=500, max_intervals=1),  # checks session once on load
])

app.clientside_callback(
    """
    function(data) {
        if (data !== null) {
            setTimeout(() => { location.reload(); }, 500); // Delay to allow storage update
        }
        return window.dash_clientside.no_update;
    }
    """,
    Output("theme-store", "data"),
    Input("theme-store", "data"), prevent_initial_call=True
)


@app.callback(
    Output("nav-dashboard", "active"),
    Output("nav-analytics", "active"),
    Output("nav-strategies", "active"),
    Output("nav-controls", "active"),
    Input("url", "pathname")
)
def update_active_links(pathname):
    return (
        pathname == "/dashboard",
        pathname == "/analytics",
        pathname == "/strategies",
        pathname == "/controls"
    )


@app.callback(
    Output("theme-store", "data", allow_duplicate=True),
    [Input(f"theme-{theme_name}", "n_clicks") for theme_name in THEMES],
    prevent_initial_call=True
)
def change_theme(*args):
    ctx = callback_context
    if not ctx.triggered:
        return dash.no_update

    clicked_id = ctx.triggered[0]["prop_id"].split(".")[0]
    selected_theme_name = clicked_id.replace("theme-", "")
    selected_theme = THEMES[selected_theme_name]

    # Update external stylesheets
    app.config.external_stylesheets = [selected_theme,
                                       "https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css"]

    # Extract and apply the figure template
    match = re.search(pattern, selected_theme)
    if match:
        load_figure_template(match.group(1))

    return {"theme": selected_theme}  # Store the theme


@app.callback(
    Output("login-logout-button", "children"),
    Output("login-logout-button", "color"),
    Input("startup-check", "n_intervals"),
)
def update_button_on_load(_):
    if session.get("user_id"):  # Example session key
        return "Logout", "danger"
    return "Login", "success"


@app.callback(
    Output("url", "href"),
    Input("login-logout-button", "n_clicks"),
    State("login-logout-button", "children"),
    prevent_initial_call=True
)
def handle_login_logout(n_clicks, label):
    if label == "Logout":
        session.clear()
    return "/login"


# Load existing callbacks
get_callbacks(app)
get_search_bar_callbacks(app)

# Load manual trade entry callbacks
from dash_app.widgets.manual_trade_entry import get_callbacks as get_manual_trade_callbacks
get_manual_trade_callbacks(app)

# Load add order modal callbacks
from dash_app.widgets.add_order_modal import get_callbacks as get_add_order_callbacks
get_add_order_callbacks(app)

if __name__ == '__main__':
    app.run(debug=False, dev_tools_hot_reload=True)
